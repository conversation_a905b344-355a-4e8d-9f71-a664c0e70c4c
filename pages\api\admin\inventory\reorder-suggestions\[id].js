import { supabaseAdmin } from '@/lib/supabase';
import { authenticateAdminRequest } from '@/lib/auth-utils';

/**
 * Reorder Suggestions API Endpoint
 * Calculates intelligent reorder recommendations based on sales history and demand patterns
 */
export default async function handler(req, res) {
  // Only allow GET requests
  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  // Authenticate request
  const { authorized, error, user } = await authenticateAdminRequest(req);
  if (!authorized) {
    return res.status(401).json({
      error: 'Unauthorized access',
      message: error?.message || 'Authentication failed'
    });
  }

  const { id } = req.query;

  if (!id) {
    return res.status(400).json({ error: 'Product ID is required' });
  }

  try {
    // Get product and inventory data
    const { data: product, error: productError } = await supabaseAdmin
      .from('products')
      .select(`
        *,
        inventory(*),
        suppliers(*)
      `)
      .eq('id', id)
      .single();

    if (productError) {
      throw productError;
    }

    if (!product) {
      return res.status(404).json({ error: 'Product not found' });
    }

    // Get sales history for demand calculation (last 365 days)
    const oneYearAgo = new Date(Date.now() - 365 * 24 * 60 * 60 * 1000).toISOString();
    
    // Check if order_items table exists and get sales data
    const { data: salesHistory, error: salesError } = await supabaseAdmin
      .from('order_items')
      .select(`
        quantity,
        price,
        orders!inner(created_at, status)
      `)
      .eq('product_id', id)
      .gte('orders.created_at', oneYearAgo)
      .in('orders.status', ['completed', 'delivered'])
      .order('orders.created_at', { ascending: false });

    // If no sales data or error, use inventory transactions as fallback
    let demandData = salesHistory || [];
    if (salesError || !salesHistory?.length) {
      const { data: transactionHistory } = await supabaseAdmin
        .from('inventory_transactions')
        .select('quantity, created_at')
        .eq('product_id', id)
        .eq('transaction_type', 'out')
        .gte('created_at', oneYearAgo)
        .order('created_at', { ascending: false });

      demandData = transactionHistory?.map(t => ({
        quantity: Math.abs(t.quantity),
        orders: { created_at: t.created_at }
      })) || [];
    }

    // Calculate demand metrics
    const totalSales = demandData.reduce((sum, item) => sum + (item.quantity || 0), 0);
    const averageDailyDemand = totalSales / 365;
    const leadTimeDays = product.suppliers?.lead_time_days || product.lead_time_days || 7;
    const currentStock = product.inventory?.quantity || 0;

    // Calculate demand variability (standard deviation)
    const demandVariability = calculateDemandVariability(demandData);
    
    // Calculate lead time demand
    const leadTimeDemand = averageDailyDemand * leadTimeDays;
    
    // Calculate safety stock (using service level of 95% = 1.65 z-score)
    const serviceLevel = 1.65; // 95% service level
    const safetyStock = Math.ceil(serviceLevel * demandVariability * Math.sqrt(leadTimeDays));
    
    // Calculate reorder point
    const recommendedReorderPoint = Math.ceil(leadTimeDemand + safetyStock);

    // Calculate Economic Order Quantity (EOQ)
    const annualDemand = totalSales || 1; // Avoid division by zero
    const orderingCost = 50; // Estimated ordering cost in AUD
    const holdingCostRate = 0.25; // 25% of item cost annually
    const unitCost = product.cost_price || product.price * 0.6 || 10;
    const holdingCost = unitCost * holdingCostRate;

    const economicOrderQuantity = Math.ceil(
      Math.sqrt((2 * annualDemand * orderingCost) / holdingCost)
    );

    // Calculate days of stock remaining
    const daysOfStock = averageDailyDemand > 0 ? Math.floor(currentStock / averageDailyDemand) : null;

    // Determine urgency level
    let urgency = 'normal';
    if (currentStock <= 0) {
      urgency = 'critical';
    } else if (currentStock <= recommendedReorderPoint * 0.5) {
      urgency = 'high';
    } else if (currentStock <= recommendedReorderPoint) {
      urgency = 'medium';
    }

    // Calculate seasonal adjustment (simplified)
    const seasonalFactor = calculateSeasonalFactor(demandData);

    const suggestions = {
      recommended_reorder_point: Math.max(recommendedReorderPoint, 1),
      economic_order_quantity: Math.max(economicOrderQuantity, 1),
      recommended_safety_stock: Math.max(safetyStock, 0),
      average_daily_demand: parseFloat(averageDailyDemand.toFixed(2)),
      analysis_period: 365,
      lead_time_demand: Math.ceil(leadTimeDemand),
      current_stock: currentStock,
      days_of_stock: daysOfStock,
      urgency: urgency,
      seasonal_factor: seasonalFactor,
      demand_variability: parseFloat(demandVariability.toFixed(2)),
      total_sales_period: totalSales,
      suggested_order_quantity: Math.max(economicOrderQuantity, recommendedReorderPoint - currentStock),
      cost_analysis: {
        unit_cost: unitCost,
        holding_cost_annual: holdingCost,
        ordering_cost: orderingCost,
        total_cost_per_order: economicOrderQuantity * unitCost
      }
    };

    return res.status(200).json({ 
      suggestions,
      product_info: {
        id: product.id,
        name: product.name,
        sku: product.sku,
        current_reorder_point: product.reorder_point,
        current_reorder_quantity: product.reorder_quantity
      }
    });

  } catch (error) {
    console.error('Reorder suggestions error:', error);
    return res.status(500).json({
      error: 'Failed to calculate reorder suggestions',
      message: error.message
    });
  }
}

/**
 * Calculate demand variability (standard deviation of daily sales)
 */
function calculateDemandVariability(salesHistory) {
  if (salesHistory.length < 2) return 0;

  // Group sales by day
  const dailySales = {};
  salesHistory.forEach(item => {
    const date = new Date(item.orders?.created_at || item.created_at).toDateString();
    dailySales[date] = (dailySales[date] || 0) + (item.quantity || 0);
  });

  const salesValues = Object.values(dailySales);
  if (salesValues.length < 2) return 0;

  const mean = salesValues.reduce((sum, val) => sum + val, 0) / salesValues.length;
  const variance = salesValues.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / salesValues.length;

  return Math.sqrt(variance);
}

/**
 * Calculate seasonal factor (simplified - based on recent vs historical average)
 */
function calculateSeasonalFactor(salesHistory) {
  if (salesHistory.length < 30) return 1.0;

  // Compare last 30 days vs overall average
  const last30Days = salesHistory.slice(0, 30);
  const recent30DaysSales = last30Days.reduce((sum, item) => sum + (item.quantity || 0), 0);
  const recentDailyAverage = recent30DaysSales / 30;

  const totalSales = salesHistory.reduce((sum, item) => sum + (item.quantity || 0), 0);
  const overallDailyAverage = totalSales / salesHistory.length;

  if (overallDailyAverage === 0) return 1.0;

  return Math.max(0.5, Math.min(2.0, recentDailyAverage / overallDailyAverage));
}
